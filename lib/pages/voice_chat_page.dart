import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/voice_chat_service.dart';
import '../services/gemini_chat_service.dart';

class VoiceChatPage extends StatefulWidget {
  const VoiceChatPage({super.key});

  @override
  State<VoiceChatPage> createState() => _VoiceChatPageState();
}

class _VoiceChatPageState extends State<VoiceChatPage>
    with TickerProviderStateMixin {
  late VoiceChatService _voiceChatService;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _voiceChatService = VoiceChatService();

    // Animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    // Initialiser avec la clé API définie dans le service Gemini
    await _voiceChatService.initialize();
  }



  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _apiKeyController.dispose();
    _messageController.dispose();
    _voiceChatService.dispose();
    super.dispose();
  }



  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _voiceChatService,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Chat Vocal avec Gemini'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            ...[
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () {
                  setState(() {
                    _showApiKeyInput = true;
                  });
                },
              ),
              IconButton(
                icon: const Icon(Icons.clear_all),
                onPressed: () {
                  _voiceChatService.clearHistory();
                  _showSnackBar('Historique effacé');
                },
              ),
            ],
          ],
        ),
        body: _showApiKeyInput ? _buildApiKeyInput() : _buildChatInterface(),
      ),
    );
  }

  Widget _buildApiKeyInput() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.key, size: 64, color: Colors.blue),
          const SizedBox(height: 24),
          const Text(
            'Configuration Gemini AI',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text(
            'Pour utiliser le chat vocal, vous devez obtenir une clé API Gemini gratuite :',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '1. Allez sur aistudio.google.com',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text('2. Connectez-vous avec votre compte Google'),
                const Text('3. Cliquez sur "Get API Key"'),
                const Text('4. Créez une nouvelle clé API'),
                const Text('5. Copiez la clé (format: AIza...)'),
              ],
            ),
          ),
          const SizedBox(height: 24),
          TextField(
            controller: _apiKeyController,
            decoration: const InputDecoration(
              labelText: 'Clé API Gemini',
              hintText: 'AIza...',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.vpn_key),
            ),
            obscureText: true,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeService,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Initialiser le Service'),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInterface() {
    return Consumer<VoiceChatService>(
      builder: (context, service, child) {
        return Column(
          children: [
            // Messages
            Expanded(child: _buildMessagesList(service)),

            // Statut
            _buildStatusBar(service),

            // Contrôles
            _buildControls(service),
          ],
        );
      },
    );
  }

  Widget _buildMessagesList(VoiceChatService service) {
    final messages = service.geminiService.messages;

    if (messages.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Appuyez sur le microphone pour commencer une conversation',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: message.isUser ? Colors.blue : Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.text,
              style: TextStyle(
                color: message.isUser ? Colors.white : Colors.black,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              message.formattedTime,
              style: TextStyle(
                color:
                    message.isUser
                        ? Colors.white.withOpacity(0.7)
                        : Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBar(VoiceChatService service) {
    String status = 'Prêt';
    Color statusColor = Colors.green;

    if (service.isListening) {
      status = 'Écoute... "${service.lastRecognizedText}"';
      statusColor = Colors.blue;
    } else if (service.isProcessing) {
      status = 'Traitement avec Gemini...';
      statusColor = Colors.orange;
    } else if (service.isSpeaking) {
      status = 'Gemini parle...';
      statusColor = Colors.purple;
    } else if (service.lastError.isNotEmpty) {
      status = 'Erreur: ${service.lastError}';
      statusColor = Colors.red;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      color: statusColor.withOpacity(0.1),
      child: Text(
        status,
        textAlign: TextAlign.center,
        style: TextStyle(color: statusColor, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildControls(VoiceChatService service) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Bouton microphone principal
          GestureDetector(
            onTap:
                service.isBusy
                    ? null
                    : () {
                      service.startVoiceConversation();
                    },
            child: AnimatedBuilder(
              animation:
                  service.isListening ? _pulseController : _waveController,
              builder: (context, child) {
                return Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: service.isBusy ? Colors.orange : Colors.blue,
                    boxShadow:
                        service.isListening
                            ? [
                              BoxShadow(
                                color: Colors.blue.withOpacity(0.3),
                                blurRadius: 20 * _pulseController.value,
                                spreadRadius: 10 * _pulseController.value,
                              ),
                            ]
                            : null,
                  ),
                  child: Icon(
                    service.isListening ? Icons.mic : Icons.mic_none,
                    color: Colors.white,
                    size: 32,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Contrôles secondaires
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: service.isBusy ? service.stopAll : null,
                icon: const Icon(Icons.stop),
                tooltip: 'Arrêter',
              ),
              IconButton(
                onPressed: service.isSpeaking ? service.stopSpeaking : null,
                icon: const Icon(Icons.volume_off),
                tooltip: 'Couper le son',
              ),
            ],
          ),

          // Input texte (optionnel)
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: const InputDecoration(
                    hintText: 'Ou tapez votre message...',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  onSubmitted: (text) {
                    if (text.isNotEmpty && !service.isBusy) {
                      service.sendTextMessage(text);
                      _messageController.clear();
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  final text = _messageController.text.trim();
                  if (text.isNotEmpty && !service.isBusy) {
                    service.sendTextMessage(text);
                    _messageController.clear();
                  }
                },
                icon: const Icon(Icons.send),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
